@echo off
chcp 65001 >nul
title 通用CEF卡密破解工具

echo.
echo ████████████████████████████████████████████████████████
echo █                                                      █
echo █           通用CEF卡密破解工具 v1.0                    █
echo █           Universal CEF License Bypass Tool          █
echo █                                                      █
echo ████████████████████████████████████████████████████████
echo.

:MENU
echo 请选择破解方式:
echo.
echo [1] Python数据库修改方式 (推荐)
echo [2] DLL Hook注入方式 (高级)
echo [3] 批量处理多个应用
echo [4] 编译C++组件
echo [5] 查看帮助
echo [0] 退出
echo.
set /p choice=请输入选择 (0-5): 

if "%choice%"=="1" goto PYTHON_METHOD
if "%choice%"=="2" goto DLL_METHOD
if "%choice%"=="3" goto BATCH_METHOD
if "%choice%"=="4" goto COMPILE
if "%choice%"=="5" goto HELP
if "%choice%"=="0" goto EXIT
goto MENU

:PYTHON_METHOD
echo.
echo ═══════════════════════════════════════════════════════════
echo 🐍 Python数据库修改方式
echo ═══════════════════════════════════════════════════════════
echo.

if not exist "CEF_Universal_Bypass.py" (
    echo ❌ 未找到 CEF_Universal_Bypass.py 文件
    pause
    goto MENU
)

echo 请输入要破解的应用程序路径:
echo 示例: D:\workspace\小红书小鸡综合+
set /p app_path=路径: 

if not exist "%app_path%" (
    echo ❌ 路径不存在: %app_path%
    pause
    goto MENU
)

echo.
echo 🚀 开始处理...
python CEF_Universal_Bypass.py "%app_path%"

echo.
echo ✅ 处理完成！请重启应用程序测试。
pause
goto MENU

:DLL_METHOD
echo.
echo ═══════════════════════════════════════════════════════════
echo 🔧 DLL Hook注入方式
echo ═══════════════════════════════════════════════════════════
echo.

if not exist "CEF_Universal_Hook.dll" (
    echo ❌ 未找到 CEF_Universal_Hook.dll 文件
    echo 💡 请先选择选项4编译C++组件
    pause
    goto MENU
)

if not exist "DLL_Injector.exe" (
    echo ❌ 未找到 DLL_Injector.exe 文件
    echo 💡 请先选择选项4编译C++组件
    pause
    goto MENU
)

echo 请选择注入方式:
echo [1] 自动查找并注入所有CEF进程
echo [2] 手动指定进程名
echo [3] 手动指定进程ID
echo.
set /p inject_choice=请选择 (1-3): 

if "%inject_choice%"=="1" (
    echo.
    echo 🔍 自动查找CEF进程并注入...
    DLL_Injector.exe CEF_Universal_Hook.dll -auto
) else if "%inject_choice%"=="2" (
    echo.
    echo 请输入进程名 (例如: 小鸡红薯.exe):
    set /p process_name=进程名: 
    DLL_Injector.exe CEF_Universal_Hook.dll -p "%process_name%"
) else if "%inject_choice%"=="3" (
    echo.
    echo 请输入进程ID:
    set /p process_id=进程ID: 
    DLL_Injector.exe CEF_Universal_Hook.dll -pid %process_id%
) else (
    echo ❌ 无效选择
    pause
    goto MENU
)

echo.
pause
goto MENU

:BATCH_METHOD
echo.
echo ═══════════════════════════════════════════════════════════
echo 📦 批量处理多个应用
echo ═══════════════════════════════════════════════════════════
echo.

if not exist "CEF_Universal_Bypass.py" (
    echo ❌ 未找到 CEF_Universal_Bypass.py 文件
    pause
    goto MENU
)

echo 请输入包含多个应用的根目录:
echo 示例: D:\Tools\CEF_Apps\
set /p root_path=根目录: 

if not exist "%root_path%" (
    echo ❌ 路径不存在: %root_path%
    pause
    goto MENU
)

echo.
echo 🚀 开始批量处理...
python CEF_Universal_Bypass.py "%root_path%" --batch

echo.
echo ✅ 批量处理完成！
pause
goto MENU

:COMPILE
echo.
echo ═══════════════════════════════════════════════════════════
echo 🔨 编译C++组件
echo ═══════════════════════════════════════════════════════════
echo.

echo 检查编译环境...

where gcc >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到GCC编译器
    echo 💡 请安装MinGW-w64或TDM-GCC
    pause
    goto MENU
)

echo ✅ 找到GCC编译器

if exist "CEF_Universal_Hook.cpp" (
    echo.
    echo 🔨 编译Hook DLL...
    gcc -shared -o CEF_Universal_Hook.dll CEF_Universal_Hook.cpp -lwininet -lws2_32
    if exist "CEF_Universal_Hook.dll" (
        echo ✅ Hook DLL编译成功
    ) else (
        echo ❌ Hook DLL编译失败
    )
) else (
    echo ❌ 未找到 CEF_Universal_Hook.cpp 文件
)

if exist "DLL_Injector.cpp" (
    echo.
    echo 🔨 编译DLL注入器...
    gcc -o DLL_Injector.exe DLL_Injector.cpp
    if exist "DLL_Injector.exe" (
        echo ✅ DLL注入器编译成功
    ) else (
        echo ❌ DLL注入器编译失败
    )
) else (
    echo ❌ 未找到 DLL_Injector.cpp 文件
)

echo.
echo 📊 编译完成！
pause
goto MENU

:HELP
echo.
echo ═══════════════════════════════════════════════════════════
echo 📖 使用帮助
echo ═══════════════════════════════════════════════════════════
echo.
echo 🎯 工具说明:
echo    这是一个通用的CEF卡密破解工具，支持所有基于CEF框架的应用
echo.
echo 🔧 破解方式:
echo    1. Python数据库修改: 直接修改CEF的本地存储数据库
echo    2. DLL Hook注入: 通过Hook网络API拦截验证请求
echo.
echo 📁 文件说明:
echo    - CEF_Universal_Bypass.py: Python破解脚本
echo    - CEF_Universal_Hook.cpp: Hook DLL源码
echo    - DLL_Injector.cpp: DLL注入器源码
echo    - Universal_CEF_Bypass.bat: 本批处理脚本
echo.
echo 💡 使用建议:
echo    1. 优先使用Python方式，简单有效
echo    2. 如果Python方式无效，尝试DLL注入方式
echo    3. 使用前请备份重要数据
echo    4. 某些杀毒软件可能误报，请添加白名单
echo.
echo 🚨 注意事项:
echo    - 本工具仅供学习研究使用
echo    - 请遵守相关法律法规
echo    - 使用本工具产生的后果由用户自行承担
echo.
pause
goto MENU

:EXIT
echo.
echo 👋 感谢使用通用CEF卡密破解工具！
echo.
pause
exit

:ERROR
echo.
echo ❌ 发生错误，请检查输入并重试
pause
goto MENU
