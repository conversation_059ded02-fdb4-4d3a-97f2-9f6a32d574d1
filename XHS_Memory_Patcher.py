#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书小鸡内存补丁工具
XHS Memory Patcher Tool
针对正在运行的小鸡红薯系统进行内存补丁，永久绕过卡密验证
"""

import os
import sys
import ctypes
import ctypes.wintypes
import psutil
import json
import time
from pathlib import Path

# Windows API 常量
PROCESS_ALL_ACCESS = 0x1F0FFF
MEM_COMMIT = 0x1000
MEM_RESERVE = 0x2000
PAGE_EXECUTE_READWRITE = 0x40
PAGE_READWRITE = 0x04

class XHSMemoryPatcher:
    def __init__(self):
        self.kernel32 = ctypes.windll.kernel32
        self.processes = []
        self.target_patterns = [
            b"license", b"auth", b"verify", b"card", b"key",
            b"\xe5\x8d\xa1\xe5\xaf\x86",  # "卡密" UTF-8
            b"\xe9\xaa\x8c\xe8\xaf\x81",  # "验证" UTF-8
            b"\xe6\x8e\x88\xe6\x9d\x83",  # "授权" UTF-8
        ]
        
    def find_xhs_processes(self):
        """查找所有小鸡红薯相关进程"""
        processes = []
        keywords = ["小鸡", "红薯", "xhs", "综合"]
        
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                proc_info = proc.info
                proc_name = proc_info['name'].lower() if proc_info['name'] else ""
                
                for keyword in keywords:
                    if keyword in proc_name:
                        processes.append({
                            'pid': proc_info['pid'],
                            'name': proc_info['name'],
                            'exe': proc_info['exe']
                        })
                        break
                        
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
                
        return processes
    
    def open_process(self, pid):
        """打开进程句柄"""
        handle = self.kernel32.OpenProcess(PROCESS_ALL_ACCESS, False, pid)
        if not handle:
            print(f"❌ 无法打开进程 {pid}, 错误码: {ctypes.GetLastError()}")
            return None
        return handle
    
    def read_process_memory(self, handle, address, size):
        """读取进程内存"""
        buffer = ctypes.create_string_buffer(size)
        bytes_read = ctypes.wintypes.DWORD(0)
        
        success = self.kernel32.ReadProcessMemory(
            handle, address, buffer, size, ctypes.byref(bytes_read)
        )
        
        if success:
            return buffer.raw[:bytes_read.value]
        return None
    
    def write_process_memory(self, handle, address, data):
        """写入进程内存"""
        bytes_written = ctypes.wintypes.DWORD(0)
        success = self.kernel32.WriteProcessMemory(
            handle, address, data, len(data), ctypes.byref(bytes_written)
        )
        return success and bytes_written.value == len(data)
    
    def scan_memory_for_patterns(self, handle, start_addr, end_addr):
        """扫描内存中的验证相关模式"""
        found_addresses = []
        chunk_size = 4096  # 4KB chunks
        
        current_addr = start_addr
        while current_addr < end_addr:
            try:
                data = self.read_process_memory(handle, current_addr, chunk_size)
                if data:
                    for pattern in self.target_patterns:
                        offset = data.find(pattern)
                        if offset != -1:
                            found_addresses.append({
                                'address': current_addr + offset,
                                'pattern': pattern,
                                'context': data[max(0, offset-20):offset+len(pattern)+20]
                            })
                
                current_addr += chunk_size
                
            except Exception as e:
                current_addr += chunk_size
                continue
                
        return found_addresses
    
    def get_process_memory_regions(self, handle):
        """获取进程内存区域"""
        regions = []
        address = 0
        
        while address < 0x7FFFFFFF:  # 用户空间上限
            mbi = ctypes.wintypes.MEMORY_BASIC_INFORMATION()
            size = self.kernel32.VirtualQueryEx(
                handle, address, ctypes.byref(mbi), ctypes.sizeof(mbi)
            )
            
            if size == 0:
                break
                
            if (mbi.State == MEM_COMMIT and 
                mbi.Protect & (PAGE_READWRITE | PAGE_EXECUTE_READWRITE)):
                regions.append({
                    'base': mbi.BaseAddress,
                    'size': mbi.RegionSize,
                    'end': mbi.BaseAddress + mbi.RegionSize
                })
            
            address = mbi.BaseAddress + mbi.RegionSize
            
        return regions
    
    def patch_verification_logic(self, handle, addresses):
        """补丁验证逻辑"""
        success_count = 0
        
        for addr_info in addresses:
            address = addr_info['address']
            pattern = addr_info['pattern']
            
            try:
                # 根据不同的模式应用不同的补丁
                if b"license" in pattern or b"auth" in pattern:
                    # 将验证函数返回值改为成功
                    patch_data = b"\x31\xC0\x40\xC3"  # xor eax,eax; inc eax; ret (返回1)
                elif b"verify" in pattern:
                    # NOP掉验证检查
                    patch_data = b"\x90" * 8  # NOP指令
                elif b"\xe5\x8d\xa1\xe5\xaf\x86" in pattern:  # "卡密"
                    # 替换为成功状态
                    patch_data = b"\xe6\x88\x90\xe5\x8a\x9f"  # "成功" UTF-8
                else:
                    # 通用补丁：NOP
                    patch_data = b"\x90" * len(pattern)
                
                if self.write_process_memory(handle, address, patch_data):
                    print(f"✅ 成功补丁地址 0x{address:08X}")
                    success_count += 1
                else:
                    print(f"❌ 补丁失败地址 0x{address:08X}")
                    
            except Exception as e:
                print(f"❌ 补丁异常 0x{address:08X}: {e}")
                
        return success_count
    
    def create_bypass_config(self, process_dir):
        """在进程目录创建绕过配置"""
        try:
            config_path = Path(process_dir) / "bypass_config.json"
            bypass_config = {
                "license_status": "verified",
                "auth_token": "BYPASSED_TOKEN",
                "user_verified": True,
                "expire_time": int(time.time()) + 365*24*3600,
                "bypass_enabled": True,
                "last_check": int(time.time())
            }
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(bypass_config, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 创建绕过配置: {config_path}")
            return True
            
        except Exception as e:
            print(f"❌ 创建配置失败: {e}")
            return False
    
    def patch_process(self, process_info):
        """补丁单个进程"""
        pid = process_info['pid']
        name = process_info['name']
        exe_path = process_info['exe']
        
        print(f"\n🔧 正在处理进程: {name} (PID: {pid})")
        
        handle = self.open_process(pid)
        if not handle:
            return False
        
        try:
            # 获取内存区域
            print("📍 扫描内存区域...")
            regions = self.get_process_memory_regions(handle)
            print(f"找到 {len(regions)} 个可写内存区域")
            
            # 扫描验证相关模式
            all_addresses = []
            for region in regions[:10]:  # 限制扫描前10个区域，避免过长时间
                addresses = self.scan_memory_for_patterns(
                    handle, region['base'], region['end']
                )
                all_addresses.extend(addresses)
            
            if all_addresses:
                print(f"🎯 找到 {len(all_addresses)} 个验证相关地址")
                
                # 应用内存补丁
                success_count = self.patch_verification_logic(handle, all_addresses)
                print(f"✅ 成功补丁 {success_count}/{len(all_addresses)} 个地址")
                
                # 创建绕过配置文件
                if exe_path:
                    process_dir = Path(exe_path).parent
                    self.create_bypass_config(process_dir)
                
                return success_count > 0
            else:
                print("⚠️ 未找到验证相关模式")
                return False
                
        except Exception as e:
            print(f"❌ 处理进程异常: {e}")
            return False
        finally:
            self.kernel32.CloseHandle(handle)
    
    def run(self):
        """运行补丁工具"""
        print("🎯 小红书小鸡内存补丁工具 v1.0")
        print("=" * 50)
        
        # 检查管理员权限
        if not ctypes.windll.shell32.IsUserAnAdmin():
            print("❌ 需要管理员权限运行此工具")
            print("💡 请右键选择'以管理员身份运行'")
            return False
        
        # 查找目标进程
        processes = self.find_xhs_processes()
        if not processes:
            print("❌ 未找到小红书小鸡相关进程")
            print("💡 请确保程序正在运行")
            return False
        
        print(f"🔍 找到 {len(processes)} 个相关进程:")
        for i, proc in enumerate(processes):
            print(f"  [{i+1}] {proc['name']} (PID: {proc['pid']})")
        
        # 处理所有进程
        success_count = 0
        for process_info in processes:
            if self.patch_process(process_info):
                success_count += 1
        
        print(f"\n📊 处理结果:")
        print(f"   总进程数: {len(processes)}")
        print(f"   成功数量: {success_count}")
        
        if success_count > 0:
            print("\n🎉 内存补丁完成！")
            print("💡 程序现在应该可以无需卡密验证运行")
            print("⚠️ 注意：重启程序后需要重新运行此工具")
        else:
            print("\n❌ 补丁失败，可能需要尝试其他方法")
        
        return success_count > 0

def main():
    try:
        patcher = XHSMemoryPatcher()
        patcher.run()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
