#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用CEF卡密破解工具
Universal CEF License Bypass Tool
适用于所有基于CEF框架的卡密验证应用
"""

import os
import sys
import json
import sqlite3
import struct
import time
from pathlib import Path
import shutil
import argparse

class CEFBypassTool:
    def __init__(self):
        self.common_paths = [
            "data/CEFLib/CacheData/GlobalData/Local Storage/leveldb/",
            "CEFLib/CacheData/GlobalData/Local Storage/leveldb/",
            "CacheData/GlobalData/Local Storage/leveldb/",
            "Local Storage/leveldb/",
            "data/CEF/Local Storage/leveldb/",
            "CEF/Local Storage/leveldb/"
        ]
        
        self.bypass_keys = [
            # 常见的验证状态键名
            "license_status", "auth_status", "verification_status",
            "card_status", "key_status", "login_status", "user_status",
            "activated", "verified", "authorized", "licensed",
            "卡密状态", "验证状态", "授权状态", "登录状态",
            "NK5A82ECC893F0D517ABB40F03B73522B1"  # 用户提供的卡密
        ]
        
        self.bypass_values = {
            "success": True,
            "verified": True,
            "authorized": True,
            "activated": True,
            "status": "success",
            "code": 200,
            "valid": True,
            "expire_time": int(time.time()) + 365*24*3600,  # 一年后过期
            "license_key": "BYPASSED_BY_UNIVERSAL_TOOL"
        }

    def find_cef_directories(self, root_path):
        """查找所有可能的CEF目录"""
        cef_dirs = []
        root = Path(root_path)
        
        # 搜索常见的CEF目录结构
        for pattern in ["**/Local Storage/leveldb", "**/LocalStorage/leveldb", 
                       "**/CEF*/Local*", "**/data/CEF*", "**/CacheData*"]:
            for path in root.glob(pattern):
                if path.is_dir():
                    cef_dirs.append(str(path))
        
        return cef_dirs

    def backup_database(self, db_path):
        """备份原始数据库"""
        backup_path = f"{db_path}.backup_{int(time.time())}"
        try:
            if os.path.exists(db_path):
                shutil.copy2(db_path, backup_path)
                print(f"✅ 已备份数据库: {backup_path}")
                return backup_path
        except Exception as e:
            print(f"⚠️ 备份失败: {e}")
        return None

    def read_leveldb_log(self, log_path):
        """读取LevelDB日志文件"""
        try:
            with open(log_path, 'rb') as f:
                data = f.read()
                # 查找可能的键值对
                text_data = data.decode('utf-8', errors='ignore')
                return text_data
        except Exception as e:
            print(f"读取日志失败: {e}")
            return None

    def modify_leveldb_files(self, leveldb_dir):
        """修改LevelDB文件"""
        modified = False
        
        # 处理.log文件
        for log_file in Path(leveldb_dir).glob("*.log"):
            try:
                with open(log_file, 'rb') as f:
                    data = f.read()
                
                # 创建新的验证数据
                bypass_data = json.dumps(self.bypass_values).encode('utf-8')
                
                # 如果文件很小，直接写入绕过数据
                if len(data) < 1024:
                    with open(log_file, 'wb') as f:
                        f.write(bypass_data)
                    modified = True
                    print(f"✅ 已修改日志文件: {log_file}")
                
            except Exception as e:
                print(f"修改日志文件失败 {log_file}: {e}")
        
        return modified

    def create_bypass_entries(self, leveldb_dir):
        """在LevelDB目录中创建绕过条目"""
        try:
            # 创建一个新的日志文件包含绕过数据
            bypass_log = Path(leveldb_dir) / "bypass.log"
            
            bypass_entries = {}
            for key in self.bypass_keys:
                bypass_entries[key] = self.bypass_values
            
            # 写入绕过数据
            with open(bypass_log, 'w', encoding='utf-8') as f:
                json.dump(bypass_entries, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 已创建绕过文件: {bypass_log}")
            return True
            
        except Exception as e:
            print(f"创建绕过条目失败: {e}")
            return False

    def modify_preferences(self, app_dir):
        """修改CEF偏好设置文件"""
        pref_files = [
            "LocalPrefs.json", "Preferences", "Local State",
            "data/CEFLib/CacheData/LocalPrefs.json",
            "CEFLib/CacheData/LocalPrefs.json"
        ]
        
        modified = False
        for pref_file in pref_files:
            pref_path = Path(app_dir) / pref_file
            if pref_path.exists():
                try:
                    with open(pref_path, 'r', encoding='utf-8') as f:
                        prefs = json.load(f)
                    
                    # 添加绕过设置
                    prefs.update({
                        "license_bypass": True,
                        "auth_bypass": True,
                        "verification_bypass": self.bypass_values
                    })
                    
                    with open(pref_path, 'w', encoding='utf-8') as f:
                        json.dump(prefs, f, ensure_ascii=False, indent=2)
                    
                    print(f"✅ 已修改偏好文件: {pref_path}")
                    modified = True
                    
                except Exception as e:
                    print(f"修改偏好文件失败 {pref_path}: {e}")
        
        return modified

    def process_application(self, app_path):
        """处理单个应用程序"""
        print(f"\n🔍 正在处理应用: {app_path}")
        
        app_path = Path(app_path)
        if not app_path.exists():
            print(f"❌ 路径不存在: {app_path}")
            return False
        
        success = False
        
        # 查找CEF目录
        cef_dirs = self.find_cef_directories(app_path)
        
        if not cef_dirs:
            print("⚠️ 未找到CEF目录，尝试通用处理...")
            # 尝试修改偏好文件
            success = self.modify_preferences(app_path)
        else:
            print(f"📁 找到 {len(cef_dirs)} 个CEF目录")
            
            for cef_dir in cef_dirs:
                print(f"🔧 处理目录: {cef_dir}")
                
                # 备份
                self.backup_database(cef_dir)
                
                # 修改LevelDB文件
                if self.modify_leveldb_files(cef_dir):
                    success = True
                
                # 创建绕过条目
                if self.create_bypass_entries(cef_dir):
                    success = True
            
            # 修改偏好文件
            if self.modify_preferences(app_path):
                success = True
        
        return success

    def batch_process(self, root_dir):
        """批量处理目录下的所有应用"""
        print(f"🚀 开始批量处理: {root_dir}")
        
        root = Path(root_dir)
        processed = 0
        success_count = 0
        
        # 查找所有可能的应用目录
        for item in root.iterdir():
            if item.is_dir():
                # 检查是否包含exe文件或CEF相关文件
                has_exe = any(item.glob("*.exe"))
                has_cef = any(item.glob("**/CEF*")) or any(item.glob("**/data/**"))
                
                if has_exe or has_cef:
                    processed += 1
                    if self.process_application(item):
                        success_count += 1
        
        print(f"\n📊 批量处理完成:")
        print(f"   处理应用数: {processed}")
        print(f"   成功数量: {success_count}")
        
        return success_count > 0

def main():
    parser = argparse.ArgumentParser(description='通用CEF卡密破解工具')
    parser.add_argument('path', help='应用程序路径或包含多个应用的目录')
    parser.add_argument('--batch', action='store_true', help='批量处理模式')
    parser.add_argument('--custom-key', help='自定义卡密值')
    
    args = parser.parse_args()
    
    tool = CEFBypassTool()
    
    # 添加自定义卡密
    if args.custom_key:
        tool.bypass_keys.append(args.custom_key)
        tool.bypass_values["custom_license_key"] = args.custom_key
    
    print("🎯 通用CEF卡密破解工具 v1.0")
    print("=" * 50)
    
    try:
        if args.batch:
            success = tool.batch_process(args.path)
        else:
            success = tool.process_application(args.path)
        
        if success:
            print("\n✅ 破解完成！请重启应用程序测试。")
        else:
            print("\n❌ 破解失败，请检查路径或尝试其他方法。")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
