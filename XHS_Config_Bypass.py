#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书小鸡配置绕过工具
XHS Config Bypass Tool
通过修改配置文件和数据库实现永久绕过
"""

import os
import sys
import json
import sqlite3
import shutil
import time
from pathlib import Path
import psutil

class XHSConfigBypass:
    def __init__(self):
        self.current_dir = Path.cwd()
        self.backup_dir = self.current_dir / "backup"
        self.bypass_data = {
            "license_verified": True,
            "auth_status": "success",
            "user_verified": True,
            "card_key": "NK5A82ECC893F0D517ABB40F03B73522B1",
            "expire_time": int(time.time()) + 365*24*3600,  # 一年后过期
            "bypass_enabled": True,
            "last_verification": int(time.time()),
            "verification_count": 999,
            "premium_user": True
        }
    
    def create_backup(self, file_path):
        """创建文件备份"""
        try:
            if not self.backup_dir.exists():
                self.backup_dir.mkdir()
            
            backup_name = f"{Path(file_path).name}.backup_{int(time.time())}"
            backup_path = self.backup_dir / backup_name
            shutil.copy2(file_path, backup_path)
            print(f"✅ 已备份: {backup_path}")
            return str(backup_path)
        except Exception as e:
            print(f"⚠️ 备份失败: {e}")
            return None
    
    def find_process_directory(self):
        """查找小鸡进程的运行目录"""
        for proc in psutil.process_iter(['pid', 'name', 'exe', 'cwd']):
            try:
                proc_info = proc.info
                if not proc_info['name']:
                    continue
                    
                name = proc_info['name'].lower()
                if any(keyword in name for keyword in ["小鸡", "红薯", "xhs"]):
                    exe_path = proc_info['exe']
                    if exe_path:
                        return Path(exe_path).parent
                        
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return None
    
    def modify_cef_storage(self, base_dir):
        """修改CEF本地存储"""
        modified = False
        
        # 常见的CEF存储路径
        storage_paths = [
            "data/CEFLib/CacheData/GlobalData/Local Storage/leveldb",
            "CEFLib/CacheData/GlobalData/Local Storage/leveldb",
            "data/Local Storage/leveldb",
            "Local Storage/leveldb"
        ]
        
        for storage_path in storage_paths:
            full_path = base_dir / storage_path
            if full_path.exists():
                print(f"🔍 找到CEF存储: {full_path}")
                
                # 创建绕过数据文件
                bypass_file = full_path / "bypass_data.json"
                try:
                    with open(bypass_file, 'w', encoding='utf-8') as f:
                        json.dump(self.bypass_data, f, ensure_ascii=False, indent=2)
                    print(f"✅ 创建绕过数据: {bypass_file}")
                    modified = True
                except Exception as e:
                    print(f"❌ 创建绕过数据失败: {e}")
                
                # 修改现有的.log文件
                for log_file in full_path.glob("*.log"):
                    if log_file.stat().st_size < 1024:  # 只修改小文件
                        self.create_backup(log_file)
                        try:
                            with open(log_file, 'w', encoding='utf-8') as f:
                                json.dump(self.bypass_data, f)
                            print(f"✅ 修改日志文件: {log_file}")
                            modified = True
                        except Exception as e:
                            print(f"❌ 修改日志文件失败: {e}")
        
        return modified
    
    def modify_preferences(self, base_dir):
        """修改偏好设置文件"""
        modified = False
        
        pref_files = [
            "data/CEFLib/CacheData/LocalPrefs.json",
            "CEFLib/CacheData/LocalPrefs.json",
            "LocalPrefs.json",
            "Preferences",
            "config.json",
            "settings.json"
        ]
        
        for pref_file in pref_files:
            pref_path = base_dir / pref_file
            if pref_path.exists():
                print(f"🔍 找到配置文件: {pref_path}")
                self.create_backup(pref_path)
                
                try:
                    # 读取现有配置
                    with open(pref_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # 添加绕过设置
                    config.update(self.bypass_data)
                    config["license_bypass"] = True
                    config["auth_bypass"] = True
                    
                    # 写回配置
                    with open(pref_path, 'w', encoding='utf-8') as f:
                        json.dump(config, f, ensure_ascii=False, indent=2)
                    
                    print(f"✅ 修改配置文件: {pref_path}")
                    modified = True
                    
                except Exception as e:
                    print(f"❌ 修改配置文件失败: {e}")
        
        return modified
    
    def create_bypass_registry(self, base_dir):
        """创建绕过注册表文件"""
        try:
            reg_file = base_dir / "bypass_registry.reg"
            reg_content = f"""Windows Registry Editor Version 5.00

[HKEY_CURRENT_USER\\Software\\XHS_Bypass]
"LicenseVerified"=dword:00000001
"AuthStatus"="success"
"CardKey"="{self.bypass_data['card_key']}"
"ExpireTime"=dword:{self.bypass_data['expire_time']:08x}
"BypassEnabled"=dword:00000001
"""
            
            with open(reg_file, 'w', encoding='utf-8') as f:
                f.write(reg_content)
            
            print(f"✅ 创建注册表文件: {reg_file}")
            print("💡 可以双击导入注册表文件")
            return True
            
        except Exception as e:
            print(f"❌ 创建注册表文件失败: {e}")
            return False
    
    def create_startup_bypass(self, base_dir):
        """创建启动绕过脚本"""
        try:
            script_file = base_dir / "startup_bypass.bat"
            script_content = f"""@echo off
echo 正在应用小红书小鸡绕过设置...

REM 设置环境变量
set XHS_LICENSE_VERIFIED=1
set XHS_AUTH_STATUS=success
set XHS_BYPASS_ENABLED=1

REM 创建绕过标记文件
echo {{"verified": true, "timestamp": {int(time.time())}}} > bypass_marker.json

echo 绕过设置已应用
"""
            
            with open(script_file, 'w', encoding='gbk') as f:
                f.write(script_content)
            
            print(f"✅ 创建启动脚本: {script_file}")
            return True
            
        except Exception as e:
            print(f"❌ 创建启动脚本失败: {e}")
            return False
    
    def patch_executable(self, base_dir):
        """尝试补丁可执行文件"""
        exe_files = list(base_dir.glob("*.exe"))
        if not exe_files:
            return False
        
        patched = False
        for exe_file in exe_files:
            if "小鸡" in exe_file.name or "红薯" in exe_file.name:
                print(f"🔍 找到可执行文件: {exe_file}")
                
                # 备份原文件
                backup_path = self.create_backup(exe_file)
                if not backup_path:
                    continue
                
                try:
                    # 读取文件内容
                    with open(exe_file, 'rb') as f:
                        data = f.read()
                    
                    # 查找并替换验证相关字符串
                    replacements = [
                        (b"license_check", b"license_pass"),
                        (b"auth_failed", b"auth_passed"),
                        (b"verify_card", b"bypass_card"),
                        (b"\xe5\x8d\xa1\xe5\xaf\x86\xe9\xaa\x8c\xe8\xaf\x81", b"\xe7\xbb\x95\xe8\xbf\x87\xe6\x88\x90\xe5\x8a\x9f"),  # "卡密验证" -> "绕过成功"
                    ]
                    
                    modified = False
                    for old_bytes, new_bytes in replacements:
                        if old_bytes in data:
                            data = data.replace(old_bytes, new_bytes)
                            modified = True
                            print(f"✅ 替换字符串: {old_bytes} -> {new_bytes}")
                    
                    if modified:
                        # 写回修改后的文件
                        with open(exe_file, 'wb') as f:
                            f.write(data)
                        print(f"✅ 补丁可执行文件: {exe_file}")
                        patched = True
                    
                except Exception as e:
                    print(f"❌ 补丁可执行文件失败: {e}")
                    # 恢复备份
                    if backup_path:
                        shutil.copy2(backup_path, exe_file)
        
        return patched
    
    def run(self):
        """运行配置绕过工具"""
        print("🎯 小红书小鸡配置绕过工具 v1.0")
        print("=" * 50)
        
        # 查找进程目录
        process_dir = self.find_process_directory()
        if not process_dir:
            print("❌ 未找到小红书小鸡进程")
            print("💡 请确保程序正在运行，或手动指定目录")
            
            # 手动输入目录
            manual_path = input("请输入程序目录路径 (回车跳过): ").strip()
            if manual_path:
                process_dir = Path(manual_path)
                if not process_dir.exists():
                    print("❌ 指定的目录不存在")
                    return False
            else:
                return False
        
        print(f"🔍 程序目录: {process_dir}")
        
        # 执行各种绕过方法
        success_count = 0
        
        print("\n📝 修改CEF存储...")
        if self.modify_cef_storage(process_dir):
            success_count += 1
        
        print("\n📝 修改配置文件...")
        if self.modify_preferences(process_dir):
            success_count += 1
        
        print("\n📝 创建注册表文件...")
        if self.create_bypass_registry(process_dir):
            success_count += 1
        
        print("\n📝 创建启动脚本...")
        if self.create_startup_bypass(process_dir):
            success_count += 1
        
        print("\n📝 尝试补丁可执行文件...")
        if self.patch_executable(process_dir):
            success_count += 1
        
        # 显示结果
        print(f"\n📊 处理结果:")
        print(f"   成功方法数: {success_count}/5")
        
        if success_count > 0:
            print("\n🎉 配置绕过完成！")
            print("💡 建议重启程序测试效果")
            print("⚠️ 如果无效，请尝试内存补丁工具")
        else:
            print("\n❌ 所有方法都失败了")
            print("💡 请尝试其他绕过方法")
        
        return success_count > 0

def main():
    try:
        bypass_tool = XHSConfigBypass()
        bypass_tool.run()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
