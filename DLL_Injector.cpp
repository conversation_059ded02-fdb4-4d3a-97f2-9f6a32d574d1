/*
通用DLL注入器
Universal DLL Injector
用于将绕过DLL注入到目标CEF应用程序
编译命令: g++ -o DLL_Injector.exe DLL_Injector.cpp
*/

#include <windows.h>
#include <tlhelp32.h>
#include <iostream>
#include <string>
#include <vector>

class DLLInjector {
private:
    std::string dllPath;
    
public:
    DLLInjector(const std::string& dll) : dllPath(dll) {}
    
    // 根据进程名查找进程ID
    DWORD FindProcessByName(const std::string& processName) {
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE) {
            return 0;
        }
        
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);
        
        if (!Process32First(hSnapshot, &pe32)) {
            CloseHandle(hSnapshot);
            return 0;
        }
        
        do {
            std::string currentProcess = pe32.szExeFile;
            std::transform(currentProcess.begin(), currentProcess.end(), 
                          currentProcess.begin(), ::tolower);
            std::string targetProcess = processName;
            std::transform(targetProcess.begin(), targetProcess.end(), 
                          targetProcess.begin(), ::tolower);
            
            if (currentProcess.find(targetProcess) != std::string::npos) {
                CloseHandle(hSnapshot);
                return pe32.th32ProcessID;
            }
        } while (Process32Next(hSnapshot, &pe32));
        
        CloseHandle(hSnapshot);
        return 0;
    }
    
    // 查找所有可能的CEF进程
    std::vector<DWORD> FindCEFProcesses() {
        std::vector<DWORD> processes;
        std::vector<std::string> cefKeywords = {
            "小鸡", "红薯", "小红书", "cef", "chrome", "electron",
            ".exe", "tool", "助手", "工具"
        };
        
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE) {
            return processes;
        }
        
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);
        
        if (!Process32First(hSnapshot, &pe32)) {
            CloseHandle(hSnapshot);
            return processes;
        }
        
        do {
            std::string processName = pe32.szExeFile;
            std::transform(processName.begin(), processName.end(), 
                          processName.begin(), ::tolower);
            
            // 检查是否包含CEF相关关键词
            for (const auto& keyword : cefKeywords) {
                if (processName.find(keyword) != std::string::npos) {
                    processes.push_back(pe32.th32ProcessID);
                    std::cout << "找到可能的CEF进程: " << pe32.szExeFile 
                             << " (PID: " << pe32.th32ProcessID << ")" << std::endl;
                    break;
                }
            }
        } while (Process32Next(hSnapshot, &pe32));
        
        CloseHandle(hSnapshot);
        return processes;
    }
    
    // 注入DLL到指定进程
    bool InjectDLL(DWORD processId) {
        // 打开目标进程
        HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
        if (!hProcess) {
            std::cout << "无法打开进程 " << processId << ", 错误码: " << GetLastError() << std::endl;
            return false;
        }
        
        // 获取DLL完整路径
        char fullPath[MAX_PATH];
        GetFullPathNameA(dllPath.c_str(), MAX_PATH, fullPath, NULL);
        
        // 在目标进程中分配内存
        LPVOID pDllPath = VirtualAllocEx(hProcess, NULL, strlen(fullPath) + 1, 
                                        MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
        if (!pDllPath) {
            std::cout << "内存分配失败, 错误码: " << GetLastError() << std::endl;
            CloseHandle(hProcess);
            return false;
        }
        
        // 写入DLL路径
        if (!WriteProcessMemory(hProcess, pDllPath, fullPath, strlen(fullPath) + 1, NULL)) {
            std::cout << "写入内存失败, 错误码: " << GetLastError() << std::endl;
            VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }
        
        // 获取LoadLibraryA地址
        HMODULE hKernel32 = GetModuleHandleA("kernel32.dll");
        LPVOID pLoadLibrary = GetProcAddress(hKernel32, "LoadLibraryA");
        
        // 创建远程线程加载DLL
        HANDLE hThread = CreateRemoteThread(hProcess, NULL, 0, 
                                           (LPTHREAD_START_ROUTINE)pLoadLibrary, 
                                           pDllPath, 0, NULL);
        if (!hThread) {
            std::cout << "创建远程线程失败, 错误码: " << GetLastError() << std::endl;
            VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }
        
        // 等待线程完成
        WaitForSingleObject(hThread, INFINITE);
        
        // 清理资源
        VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
        CloseHandle(hThread);
        CloseHandle(hProcess);
        
        std::cout << "✅ DLL注入成功到进程 " << processId << std::endl;
        return true;
    }
    
    // 批量注入到所有CEF进程
    int InjectToAllCEFProcesses() {
        std::vector<DWORD> processes = FindCEFProcesses();
        
        if (processes.empty()) {
            std::cout << "未找到CEF进程" << std::endl;
            return 0;
        }
        
        int successCount = 0;
        for (DWORD pid : processes) {
            if (InjectDLL(pid)) {
                successCount++;
            }
        }
        
        return successCount;
    }
};

void ShowUsage() {
    std::cout << "通用CEF DLL注入器 v1.0" << std::endl;
    std::cout << "用法:" << std::endl;
    std::cout << "  DLL_Injector.exe <DLL路径> [选项]" << std::endl;
    std::cout << "选项:" << std::endl;
    std::cout << "  -p <进程名>     注入到指定进程" << std::endl;
    std::cout << "  -pid <进程ID>   注入到指定进程ID" << std::endl;
    std::cout << "  -auto          自动查找并注入所有CEF进程" << std::endl;
    std::cout << "示例:" << std::endl;
    std::cout << "  DLL_Injector.exe CEF_Universal_Hook.dll -auto" << std::endl;
    std::cout << "  DLL_Injector.exe CEF_Universal_Hook.dll -p 小鸡红薯.exe" << std::endl;
}

int main(int argc, char* argv[]) {
    if (argc < 2) {
        ShowUsage();
        return 1;
    }
    
    std::string dllPath = argv[1];
    DLLInjector injector(dllPath);
    
    // 检查DLL文件是否存在
    if (GetFileAttributesA(dllPath.c_str()) == INVALID_FILE_ATTRIBUTES) {
        std::cout << "❌ DLL文件不存在: " << dllPath << std::endl;
        return 1;
    }
    
    std::cout << "🎯 通用CEF DLL注入器" << std::endl;
    std::cout << "DLL路径: " << dllPath << std::endl;
    std::cout << "=" << std::string(50, '=') << std::endl;
    
    if (argc == 2 || (argc == 3 && std::string(argv[2]) == "-auto")) {
        // 自动模式
        std::cout << "🔍 自动查找CEF进程..." << std::endl;
        int count = injector.InjectToAllCEFProcesses();
        std::cout << "📊 成功注入 " << count << " 个进程" << std::endl;
    }
    else if (argc >= 4) {
        std::string option = argv[2];
        std::string target = argv[3];
        
        if (option == "-p") {
            // 按进程名注入
            DWORD pid = injector.FindProcessByName(target);
            if (pid == 0) {
                std::cout << "❌ 未找到进程: " << target << std::endl;
                return 1;
            }
            
            if (injector.InjectDLL(pid)) {
                std::cout << "✅ 注入成功!" << std::endl;
            } else {
                std::cout << "❌ 注入失败!" << std::endl;
                return 1;
            }
        }
        else if (option == "-pid") {
            // 按进程ID注入
            DWORD pid = std::stoul(target);
            if (injector.InjectDLL(pid)) {
                std::cout << "✅ 注入成功!" << std::endl;
            } else {
                std::cout << "❌ 注入失败!" << std::endl;
                return 1;
            }
        }
        else {
            ShowUsage();
            return 1;
        }
    }
    else {
        ShowUsage();
        return 1;
    }
    
    std::cout << "\n🎉 操作完成！请测试目标应用程序。" << std::endl;
    std::cout << "💡 提示：如果需要查看详细日志，请检查 CEF_Bypass_Log.txt 文件" << std::endl;
    
    return 0;
}
