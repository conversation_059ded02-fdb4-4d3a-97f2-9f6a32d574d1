<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>群聊链接提交</title>
    <style>
        body {
            font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif;
            background-color: #f0f4f8;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
        }

        .container {
            background: white;
            padding: 2.5rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            width: 90%;
            max-width: 500px;
            text-align: center;
        }

        h1 {
            color: #2563eb;
            margin-bottom: 1.5rem;
            font-size: 1.8rem;
        }

        .input-group {
            margin: 2rem 0;
        }

        input {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid #2563eb;
            border-radius: 8px;
            font-size: 1rem;
            margin-bottom: 1rem;
            box-sizing: border-box;
        }

        input:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.3);
        }

        button {
            background-color: #2563eb;
            color: white;
            border: none;
            padding: 0.8rem 2rem;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        button:hover {
            background-color: #1d4ed8;
        }

        .notice {
            color: #ef4444;
            font-weight: bold;
            margin: 1rem 0;
            font-size: 0.9rem;
        }
        
        /* 新增返回按钮样式 */
        .back-button {
            position: absolute;
            left: 20px;
            top: 20px;
            text-decoration: none;
            color: #2563eb;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            transition: opacity 0.2s;
        }
        .back-button:hover {
            opacity: 0.8;
        }
        .back-button::before {
            content: "<";
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <a href="https://www.xiaohongshu.com" class="back-button">小红书首页</a>
    <div class="container">
        <h1>提交群聊口令</h1>
        <div class="notice">请在下方的输入框中填写您的群聊口令</div>
        
        <div class="input-group">
            <input type="text" placeholder="请输入完整的群聊口令..." required>
        </div>
        
        <button type="submit">立即提交</button>
    </div>

    <script>
         // 判断是否为emoji的代码点范围（保持原函数不变）
        function isEmoji(codePoint) {
            return (
                (codePoint >= 0x1F300 && codePoint <= 0x1F5FF) ||
                (codePoint >= 0x1F600 && codePoint <= 0x1F64F) ||
                (codePoint >= 0x1F680 && codePoint <= 0x1F6FF) ||
                (codePoint >= 0x2600 && codePoint <= 0x26FF)   ||
                (codePoint >= 0x2700 && codePoint <= 0x27BF)   ||
                (codePoint >= 0xFE00 && codePoint <= 0xFE0F)   ||
                (codePoint >= 0x1F900 && codePoint <= 0x1F9FF) ||
                (codePoint >= 0x1F1E6 && codePoint <= 0x1F1FF)
            );
        }
    
        // 将代码点转换为USC-2转义序列
        function codePointToEscaped(codePoint) {
            if (codePoint <= 0xFFFF) {
                return '\\u' + codePoint.toString(16).toUpperCase().padStart(4, '0');
            }
            const high = Math.floor((codePoint - 0x10000) / 0x400) + 0xD800;
            const low = (codePoint - 0x10000) % 0x400 + 0xDC00;
            return '\\u' + high.toString(16).toUpperCase().padStart(4, '0') + 
                   '\\u' + low.toString(16).toUpperCase().padStart(4, '0');
        }
        
        document.querySelector('button').addEventListener('click', function() {
            const input = document.querySelector('input');
            
            
            let text = input.value;
            let result = '';
            let i = 0;
            text = text.replace(/[\r\n]+/g, '');;
            
            while (i < text.length) {
                const codeUnit = text.charCodeAt(i);
                
                // 检测代理对
                if (0xD800 <= codeUnit && codeUnit <= 0xDBFF) {
                    const nextCodeUnit = text.charCodeAt(i + 1);
                    if (0xDC00 <= nextCodeUnit && nextCodeUnit <= 0xDFFF) {
                        const codePoint = (codeUnit - 0xD800) * 0x400 + 
                                        (nextCodeUnit - 0xDC00) + 0x10000;
                        
                        if (isEmoji(codePoint)) {
                            result += codePointToEscaped(codePoint);
                            i += 2;
                            continue;
                        }
                    }
                }
                
                // 处理普通字符
                const codePoint = text.codePointAt(i);
                if (isEmoji(codePoint)) {
                    result += codePointToEscaped(codePoint);
                    i += codePoint > 0xFFFF ? 2 : 1;
                } else {
                    result += text[i];
                    i++;
                }
            }
            
            console.log("[群口令]"+result);
            input.value = '';
            
            
        });
    </script>
</body>
</html>