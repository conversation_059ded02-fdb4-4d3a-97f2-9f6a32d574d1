#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索二进制文件中的字符串
Search strings in binary file
"""

import os
import sys

def search_strings_in_file(filename, search_terms):
    """在二进制文件中搜索字符串"""
    results = []
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        print(f"文件大小: {len(data)} 字节")
        
        for term in search_terms:
            # 搜索UTF-8编码的字符串
            term_bytes = term.encode('utf-8')
            offset = 0
            while True:
                pos = data.find(term_bytes, offset)
                if pos == -1:
                    break
                
                # 获取周围的上下文
                start = max(0, pos - 50)
                end = min(len(data), pos + len(term_bytes) + 50)
                context = data[start:end]
                
                # 尝试解码上下文
                try:
                    context_str = context.decode('utf-8', errors='ignore')
                except:
                    context_str = str(context)
                
                results.append({
                    'term': term,
                    'position': pos,
                    'hex_position': hex(pos),
                    'context': context_str
                })
                
                print(f"找到 '{term}' 在位置 {hex(pos)} ({pos})")
                print(f"上下文: {context_str[:100]}...")
                print("-" * 50)
                
                offset = pos + 1
        
        # 搜索ASCII字符串
        for term in search_terms:
            if term.isascii():
                term_bytes = term.encode('ascii')
                offset = 0
                while True:
                    pos = data.find(term_bytes, offset)
                    if pos == -1:
                        break
                    
                    # 检查是否已经找到过（避免重复）
                    found = False
                    for result in results:
                        if result['term'] == term and result['position'] == pos:
                            found = True
                            break
                    
                    if not found:
                        start = max(0, pos - 50)
                        end = min(len(data), pos + len(term_bytes) + 50)
                        context = data[start:end]
                        
                        try:
                            context_str = context.decode('ascii', errors='ignore')
                        except:
                            context_str = str(context)
                        
                        results.append({
                            'term': term,
                            'position': pos,
                            'hex_position': hex(pos),
                            'context': context_str
                        })
                        
                        print(f"找到 '{term}' (ASCII) 在位置 {hex(pos)} ({pos})")
                        print(f"上下文: {context_str[:100]}...")
                        print("-" * 50)
                    
                    offset = pos + 1
        
        return results
        
    except Exception as e:
        print(f"搜索失败: {e}")
        return []

def main():
    filename = "小鸡红薯综合.vmp.exe"
    
    # 搜索关键词
    search_terms = [
        # 中文关键词
        "卡密", "验证", "授权", "登录", "失败", "成功",
        # 英文关键词
        "license", "auth", "verify", "check", "login", "key", "card",
        "api", "http", "https", "post", "get", "json",
        "success", "failed", "error", "valid", "invalid"
    ]
    
    print("开始搜索验证相关字符串...")
    print("=" * 60)
    
    results = search_strings_in_file(filename, search_terms)
    
    print(f"\n搜索完成，共找到 {len(results)} 个匹配项")
    
    if results:
        print("\n重要发现:")
        for result in results[:10]:  # 只显示前10个结果
            print(f"- {result['term']} 在 {result['hex_position']}")
    else:
        print("未找到相关字符串，可能需要尝试其他方法")

if __name__ == "__main__":
    main()
