#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从二进制文件中提取所有可读字符串并保存到txt文件
Extract all readable strings from binary file and save to txt
"""

import re
import os

def extract_strings(filename, min_length=4):
    """提取二进制文件中的字符串"""
    strings_found = []
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        print(f"正在分析文件: {filename}")
        print(f"文件大小: {len(data)} 字节")
        
        # 提取ASCII字符串
        ascii_strings = re.findall(b'[!-~]{' + str(min_length).encode() + b',}', data)
        for i, s in enumerate(ascii_strings):
            try:
                decoded = s.decode('ascii')
                offset = data.find(s)
                strings_found.append({
                    'type': 'ASCII',
                    'offset': offset,
                    'hex_offset': hex(offset),
                    'string': decoded,
                    'length': len(decoded)
                })
            except:
                continue
        
        # 提取UTF-8字符串
        utf8_pattern = rb'[\x20-\x7E\xC0-\xDF][\x80-\xBF]*'
        utf8_matches = re.finditer(utf8_pattern, data)
        for match in utf8_matches:
            try:
                s = match.group()
                if len(s) >= min_length:
                    decoded = s.decode('utf-8')
                    if decoded.isprintable():
                        strings_found.append({
                            'type': 'UTF-8',
                            'offset': match.start(),
                            'hex_offset': hex(match.start()),
                            'string': decoded,
                            'length': len(decoded)
                        })
            except:
                continue
        
        # 提取Unicode字符串 (UTF-16LE)
        unicode_strings = re.findall(b'(?:[!-~]\x00){' + str(min_length).encode() + b',}', data)
        for s in unicode_strings:
            try:
                decoded = s.decode('utf-16le')
                offset = data.find(s)
                strings_found.append({
                    'type': 'UTF-16LE',
                    'offset': offset,
                    'hex_offset': hex(offset),
                    'string': decoded,
                    'length': len(decoded)
                })
            except:
                continue
        
        return strings_found
        
    except Exception as e:
        print(f"提取字符串失败: {e}")
        return []

def save_strings_to_file(strings_list, output_file):
    """保存字符串到文件"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("小鸡红薯综合.vmp.exe 字符串提取结果\n")
            f.write("=" * 80 + "\n\n")
            
            # 按偏移量排序
            strings_list.sort(key=lambda x: x['offset'])
            
            # 分类保存
            categories = {
                'verification': ['卡密', '验证', '授权', '登录', 'license', 'auth', 'verify', 'check', 'login', 'key', 'card'],
                'network': ['http', 'https', 'api', 'post', 'get', 'json', 'url', 'server', 'request'],
                'status': ['success', 'failed', 'error', 'valid', 'invalid', '成功', '失败', '错误'],
                'other': []
            }
            
            categorized = {cat: [] for cat in categories}
            
            for string_info in strings_list:
                string_lower = string_info['string'].lower()
                categorized_flag = False
                
                for category, keywords in categories.items():
                    if category == 'other':
                        continue
                    for keyword in keywords:
                        if keyword in string_lower:
                            categorized[category].append(string_info)
                            categorized_flag = True
                            break
                    if categorized_flag:
                        break
                
                if not categorized_flag:
                    categorized['other'].append(string_info)
            
            # 写入分类结果
            for category, strings in categorized.items():
                if not strings:
                    continue
                    
                f.write(f"\n{'='*20} {category.upper()} 相关字符串 {'='*20}\n\n")
                
                for string_info in strings:
                    f.write(f"地址: {string_info['hex_offset']} ({string_info['offset']})\n")
                    f.write(f"类型: {string_info['type']}\n")
                    f.write(f"长度: {string_info['length']}\n")
                    f.write(f"内容: {string_info['string']}\n")
                    f.write("-" * 60 + "\n")
            
            # 写入完整列表
            f.write(f"\n{'='*20} 完整字符串列表 {'='*20}\n\n")
            for i, string_info in enumerate(strings_list):
                f.write(f"{i+1:4d}. [{string_info['hex_offset']}] {string_info['type']}: {string_info['string']}\n")
        
        print(f"字符串已保存到: {output_file}")
        return True
        
    except Exception as e:
        print(f"保存文件失败: {e}")
        return False

def main():
    input_file = "小鸡红薯综合.vmp.exe"
    output_file = "小鸡字符串分析.txt"
    
    if not os.path.exists(input_file):
        print(f"错误: 找不到文件 {input_file}")
        return
    
    print("开始提取字符串...")
    strings_list = extract_strings(input_file, min_length=3)
    
    if strings_list:
        print(f"共找到 {len(strings_list)} 个字符串")
        
        # 保存到文件
        if save_strings_to_file(strings_list, output_file):
            print(f"\n✅ 分析完成！")
            print(f"📄 结果已保存到: {output_file}")
            print(f"🔍 请打开该文件查找验证相关的字符串")
        else:
            print("❌ 保存文件失败")
    else:
        print("❌ 未找到任何字符串")

if __name__ == "__main__":
    main()
