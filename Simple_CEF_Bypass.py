#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版CEF卡密破解工具
Simple CEF License Bypass Tool
无需外部依赖，直接修改配置文件实现绕过
"""

import os
import sys
import json
import time
import shutil
from pathlib import Path

class SimpleCEFBypass:
    def __init__(self):
        self.bypass_data = {
            "license_verified": True,
            "auth_status": "success",
            "user_verified": True,
            "card_key": "NK5A82ECC893F0D517ABB40F03B73522B1",
            "expire_time": int(time.time()) + 365*24*3600,
            "bypass_enabled": True,
            "verification_success": True,
            "premium_user": True,
            "valid": True,
            "authorized": True,
            "activated": True,
            "status": "success",
            "code": 200
        }
    
    def create_backup(self, file_path):
        """创建备份文件"""
        try:
            backup_path = f"{file_path}.backup_{int(time.time())}"
            shutil.copy2(file_path, backup_path)
            print(f"✅ 已备份: {backup_path}")
            return True
        except Exception as e:
            print(f"⚠️ 备份失败: {e}")
            return False
    
    def modify_cef_config(self, app_dir):
        """修改CEF配置文件"""
        app_path = Path(app_dir)
        modified_count = 0
        
        # 查找并修改配置文件
        config_patterns = [
            "**/LocalPrefs.json",
            "**/Preferences",
            "**/Local State",
            "**/config.json",
            "**/settings.json"
        ]
        
        for pattern in config_patterns:
            for config_file in app_path.glob(pattern):
                if config_file.is_file():
                    print(f"🔍 找到配置文件: {config_file}")
                    
                    try:
                        # 备份原文件
                        self.create_backup(config_file)
                        
                        # 读取现有配置
                        with open(config_file, 'r', encoding='utf-8') as f:
                            try:
                                config = json.load(f)
                            except json.JSONDecodeError:
                                # 如果不是JSON格式，创建新的配置
                                config = {}
                        
                        # 添加绕过设置
                        config.update(self.bypass_data)
                        config["license_bypass"] = True
                        config["auth_bypass"] = True
                        config["verification_bypass"] = self.bypass_data
                        
                        # 写回配置
                        with open(config_file, 'w', encoding='utf-8') as f:
                            json.dump(config, f, ensure_ascii=False, indent=2)
                        
                        print(f"✅ 已修改配置文件: {config_file}")
                        modified_count += 1
                        
                    except Exception as e:
                        print(f"❌ 修改配置文件失败 {config_file}: {e}")
        
        return modified_count > 0
    
    def create_bypass_files(self, app_dir):
        """创建绕过文件"""
        app_path = Path(app_dir)
        created_count = 0
        
        # 在各个可能的位置创建绕过文件
        bypass_locations = [
            app_path,
            app_path / "data",
            app_path / "data" / "CEFLib",
            app_path / "data" / "CEFLib" / "CacheData",
            app_path / "data" / "CEFLib" / "CacheData" / "GlobalData",
            app_path / "data" / "CEFLib" / "CacheData" / "GlobalData" / "Local Storage",
            app_path / "data" / "CEFLib" / "CacheData" / "GlobalData" / "Local Storage" / "leveldb"
        ]
        
        for location in bypass_locations:
            if location.exists():
                try:
                    # 创建绕过数据文件
                    bypass_file = location / "bypass_data.json"
                    with open(bypass_file, 'w', encoding='utf-8') as f:
                        json.dump(self.bypass_data, f, ensure_ascii=False, indent=2)
                    
                    print(f"✅ 创建绕过文件: {bypass_file}")
                    created_count += 1
                    
                    # 创建验证标记文件
                    marker_file = location / "license_verified.txt"
                    with open(marker_file, 'w', encoding='utf-8') as f:
                        f.write(f"VERIFIED_AT_{int(time.time())}")
                    
                    print(f"✅ 创建验证标记: {marker_file}")
                    
                except Exception as e:
                    print(f"❌ 创建绕过文件失败 {location}: {e}")
        
        return created_count > 0
    
    def create_registry_file(self, app_dir):
        """创建注册表绕过文件"""
        try:
            app_path = Path(app_dir)
            reg_file = app_path / "license_bypass.reg"
            
            reg_content = f"""Windows Registry Editor Version 5.00

[HKEY_CURRENT_USER\\Software\\CEF_License_Bypass]
"LicenseVerified"=dword:00000001
"AuthStatus"="success"
"CardKey"="{self.bypass_data['card_key']}"
"ExpireTime"=dword:{self.bypass_data['expire_time']:08x}
"BypassEnabled"=dword:00000001
"UserVerified"=dword:00000001
"PremiumUser"=dword:00000001

[HKEY_CURRENT_USER\\Software\\XHS_Tools]
"LicenseStatus"="verified"
"AuthToken"="BYPASSED_TOKEN"
"LastCheck"=dword:{int(time.time()):08x}
"""
            
            with open(reg_file, 'w', encoding='utf-8') as f:
                f.write(reg_content)
            
            print(f"✅ 创建注册表文件: {reg_file}")
            print("💡 双击此文件可导入注册表设置")
            return True
            
        except Exception as e:
            print(f"❌ 创建注册表文件失败: {e}")
            return False
    
    def create_startup_script(self, app_dir):
        """创建启动绕过脚本"""
        try:
            app_path = Path(app_dir)
            script_file = app_path / "start_with_bypass.bat"
            
            script_content = f"""@echo off
title 小红书小鸡 - 绕过启动
echo 正在启动小红书小鸡工具...
echo 应用绕过设置中...

REM 设置环境变量
set LICENSE_VERIFIED=1
set AUTH_STATUS=success
set BYPASS_ENABLED=1
set USER_VERIFIED=1

REM 创建临时验证文件
echo {{"verified": true, "timestamp": {int(time.time())}}} > temp_license.json

REM 查找并启动主程序
for %%f in (*.exe) do (
    if /i "%%f" neq "start_with_bypass.bat" (
        echo 启动程序: %%f
        start "" "%%f"
        goto :end
    )
)

:end
echo 绕过设置已应用，程序已启动
timeout /t 3 >nul
"""
            
            with open(script_file, 'w', encoding='gbk') as f:
                f.write(script_content)
            
            print(f"✅ 创建启动脚本: {script_file}")
            print("💡 使用此脚本启动程序可自动应用绕过设置")
            return True
            
        except Exception as e:
            print(f"❌ 创建启动脚本失败: {e}")
            return False
    
    def patch_string_in_files(self, app_dir):
        """在文件中替换验证相关字符串"""
        app_path = Path(app_dir)
        patched_count = 0
        
        # 查找可能包含验证逻辑的文件
        target_files = []
        for ext in ['*.js', '*.json', '*.txt', '*.cfg', '*.ini']:
            target_files.extend(app_path.glob(f"**/{ext}"))
        
        # 字符串替换映射
        replacements = {
            '"license_check"': '"license_pass"',
            '"auth_failed"': '"auth_success"',
            '"verify_card"': '"bypass_card"',
            '"verification_failed"': '"verification_success"',
            '"invalid_license"': '"valid_license"',
            '"expired"': '"valid"',
            '"unauthorized"': '"authorized"',
            'false': 'true',  # 谨慎使用
        }
        
        for file_path in target_files:
            if file_path.stat().st_size > 10*1024*1024:  # 跳过大于10MB的文件
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                original_content = content
                for old_str, new_str in replacements.items():
                    if old_str in content:
                        content = content.replace(old_str, new_str)
                
                if content != original_content:
                    # 备份原文件
                    self.create_backup(file_path)
                    
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    print(f"✅ 已修补文件: {file_path}")
                    patched_count += 1
                    
            except Exception as e:
                continue  # 忽略无法处理的文件
        
        return patched_count > 0
    
    def run(self, app_dir=None):
        """运行简化绕过工具"""
        print("🎯 简化版CEF卡密破解工具 v1.0")
        print("=" * 50)
        
        if not app_dir:
            app_dir = input("请输入应用程序目录路径: ").strip()
            if not app_dir:
                app_dir = "."  # 默认当前目录
        
        app_path = Path(app_dir)
        if not app_path.exists():
            print(f"❌ 目录不存在: {app_dir}")
            return False
        
        print(f"🔍 处理目录: {app_path.absolute()}")
        
        success_count = 0
        
        print("\n📝 修改CEF配置文件...")
        if self.modify_cef_config(app_dir):
            success_count += 1
        
        print("\n📝 创建绕过文件...")
        if self.create_bypass_files(app_dir):
            success_count += 1
        
        print("\n📝 创建注册表文件...")
        if self.create_registry_file(app_dir):
            success_count += 1
        
        print("\n📝 创建启动脚本...")
        if self.create_startup_script(app_dir):
            success_count += 1
        
        print("\n📝 修补配置文件...")
        if self.patch_string_in_files(app_dir):
            success_count += 1
        
        print(f"\n📊 处理结果:")
        print(f"   成功方法数: {success_count}/5")
        
        if success_count > 0:
            print("\n🎉 绕过设置完成！")
            print("💡 建议步骤:")
            print("   1. 双击 license_bypass.reg 导入注册表")
            print("   2. 使用 start_with_bypass.bat 启动程序")
            print("   3. 或者重启原程序测试效果")
        else:
            print("\n❌ 所有方法都失败了")
            print("💡 请检查目录路径是否正确")
        
        return success_count > 0

def main():
    if len(sys.argv) > 1:
        app_dir = sys.argv[1]
    else:
        app_dir = None
    
    try:
        bypass_tool = SimpleCEFBypass()
        bypass_tool.run(app_dir)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
